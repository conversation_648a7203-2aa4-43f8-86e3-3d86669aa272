using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace 工具箱
{
    public class SystemCleaner
    {
        public event Action<string> OnFileDeleted;
        public event Action<int, int> OnProgressChanged;
        public event Action<long> OnCleaningComplete;

        public async Task CleanAsync()
        {
            long totalBytesCleaned = 0;
            var tempPaths = new List<string>
            {
                Path.GetTempPath(),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Temp")
            };

            List<string> filesToDelete = new List<string>();
            foreach (var path in tempPaths)
            {
                if (Directory.Exists(path))
                {
                    filesToDelete.AddRange(Directory.GetFiles(path, "*", SearchOption.AllDirectories));
                }
            }

            int totalFiles = filesToDelete.Count;
            for (int i = 0; i < totalFiles; i++)
            {
                var file = filesToDelete[i];
                try
                {
                    var fileInfo = new FileInfo(file);
                    long fileSize = fileInfo.Length;
                    fileInfo.Delete();
                    totalBytesCleaned += fileSize;
                    OnFileDeleted?.Invoke(file);
                }
                catch (Exception)
                {
                    // 忽略无法删除的文件 (可能被占用)
                }
                OnProgressChanged?.Invoke(i + 1, totalFiles);
                await Task.Delay(1); // 给UI一点响应时间
            }

            OnCleaningComplete?.Invoke(totalBytesCleaned);
        }
    }
}